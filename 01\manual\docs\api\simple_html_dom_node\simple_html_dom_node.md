---
title: simple_html_dom_node
---

# simple_html_dom_node

Represents a single node in the DOM tree (see [`simple_html_dom`](../../simple_html_dom/simple_html_dom/)).

# Public Properties

| Property      | Description
| --------      | -----------
| `_`           | Node meta data (i.e. type of node).
| `attr`        | List of attributes.
| `children`    | List of child nodes.
| `nodes`       | List of nodes.
| `nodetype`    | Node type.
| `parent`      | Parent node object.
| `tag`         | Node's tag name.
| `tag_start`   | Start position of the tag name in the original document.

# Protected Properties

None.

# Private Properties

| Property      | Description
| --------      | -----------
| `dom`         | The DOM object (see [`simple_html_dom`](../../simple_html_dom/simple_html_dom/)).