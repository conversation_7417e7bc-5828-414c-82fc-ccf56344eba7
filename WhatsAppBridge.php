<?php
ini_set('memory_limit', '512M');

include_once('simple_html_dom.php');


$html = file_get_html('https://blog.whatsapp.com/');

        // Use a regular expression to extract the subject, body, and URL from the HTML content
        // $pattern = '/Subject=([^&]+)&amp;body=(.+?)(http:\\\\\/\\\\\/[^\\s"]+)(.*)/i';
        
        $pattern = '/"_aof4">\\\\u003Cp>(August 27, 2025)/';

        preg_match_all($pattern, $html, $matches);
        $item = [];

        // Assign the matche to more speaking varaible names and convert them to UTF-8
        var_dump($matches[1][0]);
