<?php

class WhatsAppBlogBridge extends BridgeAbstract
{
    const NAME = 'WhatsApp Blog';
    const URI = 'https://blog.whatsapp.com/';
    const DESCRIPTION = 'WhatsApp Blog';
    const MAINTAINER = 'latz';
    const CACHE_TIMEOUT = 3600; // 1h

    public function collectData()
    {
        $html = file_get_html('https://blog.whatsapp.com/');

        // Use a regular expression to extract the subject, body, and URL from the HTML content
        $pattern = '/Subject=([^&]+)&amp;body=(.+?)(http:\\\\\/\\\\\/[^\\s"]+)/i';
        preg_match_all($pattern, $html, $matches);
        $item = [];

        // Assign the matche to more speaking varaible names and convert them to UTF-8
        for ($i = 0; $i < count($matches[0]); $i++) {
            $subject = html_entity_decode($matches[1][$i], ENT_QUOTES | ENT_HTML5, 'UTF-8');
            $body = html_entity_decode($matches[2][$i], ENT_QUOTES | ENT_HTML5, 'UTF-8');
            $url = html_entity_decode($matches[3][$i], ENT_QUOTES | ENT_HTML5, 'UTF-8');
            $url = str_replace('\/', '/', $url);
            
            $item['title'] = $subject;
            $item['uri'] = $url;
            $item['timestamp'] = mktime(0, 0, 0, $month, $day, $year);
            $item['content'] = $body; // This isn't good HTML style, but at least syntactically correct
            $item['uid'] = 'sdsdjsadjsadjsadjsad';
            $this->items[] = $item;
        }
    }
}
