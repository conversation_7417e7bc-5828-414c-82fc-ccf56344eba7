<?php
ini_set('memory_limit', '512M');

include_once('simple_html_dom.php');


 $html = file_get_html('whatsapp.html');

// Use a regular expression to extract the subject, body, and URL from the HTML content
$pattern = '/Subject=([^&]+)&amp;body=(.+?)(http:\\\\\/\\\\\/[^\\s"]+)/i';
preg_match_all($pattern, $html, $matches);

// Assign the matche to more speaking varaible names and convert them to UTF-8
for ($i = 0; $i < count($matches[0]); $i++) {
     $subject = html_entity_decode($matches[1][$i], ENT_QUOTES | ENT_HTML5, 'UTF-8');
    $body = html_entity_decode($matches[2][$i], ENT_QUOTES | ENT_HTML5, 'UTF-8');
    $url = html_entity_decode($matches[3][$i], ENT_QUOTES | ENT_HTML5, 'UTF-8');
    $url = str_replace('\/', '/', $url);
}
