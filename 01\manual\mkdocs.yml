site_name: Simple HTML DOM documentation
site_url: http://simplehtmldom.sourceforge.net/
site_description: A simple HTML DOM parser written in PHP

repo_name: SourceForge
repo_url: https://sourceforge.net/projects/simplehtmldom/

theme:
  name: readthedocs
  custom_dir: 'custom_theme/'

google_analytics: ['UA-3452027-2', 'simplehtmldom.sourceforge.net']

extra_css: [extra.css]

nav:
  - 'index.md'
  - 'requirements.md'
  - 'quick-start.md'
  - 'faq.md'
  - Manual:
    - 'manual/creating-dom-objects.md'
    - 'manual/finding-html-elements.md'
    - 'manual/accessing-element-attributes.md'
    - 'manual/traversing-dom-tree.md'
    - 'manual/saving-dom-objects.md'
    - 'manual/customizing-parsing-behavior.md'
    - 'manual/adding-nodes.md'
  - API:
    - 'api/api.md'
    - 'api/constants.md'
    - 'api/definitions.md'
    - 'api/str_get_html.md'
    - 'api/file_get_html.md'
    - simple_html_dom:
      - 'api/simple_html_dom/simple_html_dom.md'
      - 'api/simple_html_dom/__construct.md'
      - 'api/simple_html_dom/__destruct.md'
      - 'api/simple_html_dom/load.md'
      - 'api/simple_html_dom/load_file.md'
      - 'api/simple_html_dom/set_callback.md'
      - 'api/simple_html_dom/remove_callback.md'
      - 'api/simple_html_dom/save.md'
      - 'api/simple_html_dom/find.md'
      - 'api/simple_html_dom/clear.md'
      - 'api/simple_html_dom/dump.md'
      - 'api/simple_html_dom/prepare.md'
      - 'api/simple_html_dom/parse.md'
      - 'api/simple_html_dom/parse_charset.md'
      - 'api/simple_html_dom/read_tag.md'
      - 'api/simple_html_dom/parse_attr.md'
      - 'api/simple_html_dom/link_nodes.md'
      - 'api/simple_html_dom/as_text_node.md'
      - 'api/simple_html_dom/skip.md'
      - 'api/simple_html_dom/copy_skip.md'
      - 'api/simple_html_dom/copy_until.md'
      - 'api/simple_html_dom/copy_until_char.md'
      - 'api/simple_html_dom/remove_noise.md'
      - 'api/simple_html_dom/restore_noise.md'
      - 'api/simple_html_dom/search_noise.md'
      - 'api/simple_html_dom/__toString.md'
      - 'api/simple_html_dom/__get.md'
      - 'api/simple_html_dom/childNodes.md'
      - 'api/simple_html_dom/firstChild.md'
      - 'api/simple_html_dom/lastChild.md'
      - 'api/simple_html_dom/createElement.md'
      - 'api/simple_html_dom/createTextNode.md'
      - 'api/simple_html_dom/getElementById.md'
      - 'api/simple_html_dom/getElementsById.md'
      - 'api/simple_html_dom/getElementByTagName.md'
      - 'api/simple_html_dom/getElementsByTagName.md'
      - 'api/simple_html_dom/loadFile.md'
    - simple_html_dom_node:
      - 'api/simple_html_dom_node/simple_html_dom_node.md'
      - 'api/simple_html_dom_node/__construct.md'
      - 'api/simple_html_dom_node/__destruct.md'
      - 'api/simple_html_dom_node/__get.md'
      - 'api/simple_html_dom_node/__isset.md'
      - 'api/simple_html_dom_node/__set.md'
      - 'api/simple_html_dom_node/__toString.md'
      - 'api/simple_html_dom_node/__unset.md'
      - 'api/simple_html_dom_node/addClass.md'
      - 'api/simple_html_dom_node/appendChild.md'
      - 'api/simple_html_dom_node/childNodes.md'
      - 'api/simple_html_dom_node/children.md'
      - 'api/simple_html_dom_node/clear.md'
      - 'api/simple_html_dom_node/convert_text.md'
      - 'api/simple_html_dom_node/dump.md'
      - 'api/simple_html_dom_node/dump_node.md'
      - 'api/simple_html_dom_node/find.md'
      - 'api/simple_html_dom_node/find_ancestor_tag.md'
      - 'api/simple_html_dom_node/first_child.md'
      - 'api/simple_html_dom_node/firstChild.md'
      - 'api/simple_html_dom_node/get_display_size.md'
      - 'api/simple_html_dom_node/getAllAttributes.md'
      - 'api/simple_html_dom_node/getAttribute.md'
      - 'api/simple_html_dom_node/getElementById.md'
      - 'api/simple_html_dom_node/getElementByTagName.md'
      - 'api/simple_html_dom_node/getElementsById.md'
      - 'api/simple_html_dom_node/getElementsByTagName.md'
      - 'api/simple_html_dom_node/has_child.md'
      - 'api/simple_html_dom_node/hasAttribute.md'
      - 'api/simple_html_dom_node/hasChildNodes.md'
      - 'api/simple_html_dom_node/hasClass.md'
      - 'api/simple_html_dom_node/innertext.md'
      - 'api/simple_html_dom_node/is_utf8.md'
      - 'api/simple_html_dom_node/last_child.md'
      - 'api/simple_html_dom_node/lastChild.md'
      - 'api/simple_html_dom_node/makeup.md'
      - 'api/simple_html_dom_node/match.md'
      - 'api/simple_html_dom_node/next_sibling.md'
      - 'api/simple_html_dom_node/nextSibling.md'
      - 'api/simple_html_dom_node/nodeName.md'
      - 'api/simple_html_dom_node/outertext.md'
      - 'api/simple_html_dom_node/parent.md'
      - 'api/simple_html_dom_node/parentNode.md'
      - 'api/simple_html_dom_node/parse_selector.md'
      - 'api/simple_html_dom_node/prev_sibling.md'
      - 'api/simple_html_dom_node/prevSibling.md'
      - 'api/simple_html_dom_node/remove.md'
      - 'api/simple_html_dom_node/removeAttribute.md'
      - 'api/simple_html_dom_node/removeChild.md'
      - 'api/simple_html_dom_node/removeClass.md'
      - 'api/simple_html_dom_node/save.md'
      - 'api/simple_html_dom_node/seek.md'
      - 'api/simple_html_dom_node/setAttribute.md'
      - 'api/simple_html_dom_node/text.md'
      - 'api/simple_html_dom_node/xmltext.md'

docs_dir: 'docs'